<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class JobPosting extends Model
{
    use HasFactory;

    /**
     * Nama tabel yang terhubung dengan model.
     * Laravel akan mengasumsikan 'jobs' jika tidak didefinisikan.
     * @var string
     */
    protected $table = 'jobs'; // Ganti jika nama tabel Anda berbeda

    /**
     * Atribut yang dapat diisi secara massal (mass assignable).
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'employer_id',
        'title',
        'category_id',
        'classification_id',
        'job_type',
        'location',
        'is_remote',
        'description',
        'responsibilities',
        'qualifications',
        'benefits',
        'min_salary',
        'max_salary',
        'currency',
        'is_salary_displayed',
        'application_deadline',
        'is_active',
        'views_count',
        'applicants_count',
        'tahun_pengalaman',
        'jam_kerja',
        'pendidikan_tipe',
        'pendidikan_id',
        'id_kelurahan',
        'id_kecamatan',
        'id_kota',
        'id_provinsi',
        'cv_required',
        'pay_type',
        'hide_salary',
    ];

    /**
     * Atribut yang harus di-cast ke tipe data tertentu.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_remote' => 'boolean',
        'min_salary' => 'decimal:2',
        'max_salary' => 'decimal:2',
        'is_salary_displayed' => 'boolean',
        'application_deadline' => 'date',
        'is_active' => 'boolean',
        'cv_required' => 'boolean',
        'hide_salary' => 'boolean',
    ];

    /**
     * Mendefinisikan relasi "belongsTo" ke model Employer.
     */
    public function employer(): BelongsTo
    {
        return $this->belongsTo(Employer::class, 'employer_id');
    }
}
